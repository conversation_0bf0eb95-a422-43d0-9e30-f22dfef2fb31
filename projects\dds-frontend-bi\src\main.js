import "./set-public-path"
import "@tailwindcss/postcss7-compat/tailwind.css"
import "@/assets/css/highlight.scss"
import "@/assets/css/github-markdown.scss"

import Vue from "vue"
import App from "./App.vue"
import Store from "./store"
import I18n from "./i18n"
import Router from "./router"
import SingleSpaVue from "single-spa-vue"
import Request from "@/service"
import htmlToPdf from "@/utils/htmlToPdf"
import "./filters"
import "./directives/tooltip"
Vue.use(htmlToPdf)
import Avue from "@smallwei/avue"
import "@smallwei/avue/lib/index.css"
Vue.use(Avue)
import "./assets/icons" // icon
// 引入全局样式
import "@/assets/css/index.scss"

// 引入Luckysheet样式
import "luckysheet/dist/css/luckysheet.css"
import "luckysheet/dist/assets/iconfont/iconfont.css"
import "luckysheet/dist/plugins/css/pluginsCss.css"
import "luckysheet/dist/plugins/plugins.css"

const dayjs = require("dayjs")
Vue.prototype.$dayjs = dayjs
Vue.prototype.$httpBi = Request
import ElSelectV2 from "el-select-v2"

Vue.use(ElSelectV2)
// 引入jshint用于实现js自动补全提示

import jshint from "jshint"

window.JSHINT = jshint.JSHINT

// vxe-table
import "xe-utils"
import VXETable from "vxe-table"
import "vxe-table/lib/style.css"
import dataV from "@jiaminghi/data-view"
Vue.use(dataV)

// lodash
import _ from "lodash"
Vue.prototype._ = _

Vue.use(VXETable)

const vueLifecycles = new SingleSpaVue({
  Vue,
  appOptions: {
    el: "#single-spa-projects",
    store: Store,
    i18n: I18n,
    router: Router,
    render: h => h(App)
  }
})

// 退出登录
const listener_logout = () => Store.dispatch("clearUser")

/**
 * singleSpa生命周期 - 初始化
 */

function changeCollapseListener(e) {
  Store.dispatch("changeIsCollapse", e.detail.isCollapse)
}
export const bootstrap = vueLifecycles.bootstrap

/**
 * singleSpa生命周期 - 加载
 */
export function mount(props) {
  // 添加监听 - 切换语言环境
  Vue.prototype.$utils.notice.logout.listener(listener_logout)
  window.addEventListener("changeCollapse", changeCollapseListener)
  return vueLifecycles.mount(props)
}

/**
 * singleSpa生命周期 - 卸载
 */
export function unmount(props) {
  // 添加监听 - 切换语言环境
  Vue.prototype.$utils.notice.logout.clear(listener_logout)
  window.removeEventListener("changeCollapse", changeCollapseListener)
  return vueLifecycles.unmount(props)
}
