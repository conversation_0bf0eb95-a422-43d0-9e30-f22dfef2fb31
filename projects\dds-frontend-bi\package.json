{"name": "dds_bi", "version": "1.3.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 13582", "build": "vue-cli-service build", "build:hnnd": "vue-cli-service build --mode hnnd", "lint": "vue-cli-service lint", "analyzer": "cross-env use_analyzer=true npm run build:hnnd"}, "dependencies": {"@antv/g6": "^4.8.25", "@jiaminghi/data-view": "^2.10.0", "@riophae/vue-treeselect": "^0.4.0", "@smallwei/avue": "^2.10.8", "@tailwindcss/postcss7-compat": "^2.2.17", "@traptitech/markdown-it-katex": "^3.6.0", "@wangeditor/editor": "^5.1.14", "@wangeditor/editor-for-vue": "^1.0.2", "autoprefixer": "^9.8.8", "axios": "0.19.2", "babel-plugin-transform-remove-console": "^6.9.4", "brace": "^0.11.1", "core-js": "^3.20.1", "crypto-js": "^4.1.1", "dayjs": "^1.10.7", "echarts": "^5.3.2", "echarts-liquidfill": "^3.1.0", "echarts-wordcloud": "^2.0.0", "el-select-v2": "^2.0.3", "element-resize-detector": "^1.2.4", "element-ui": "2.13.0", "file-saver": "^2.0.5", "highlight.js": "^11.7.0", "html2canvas": "^1.4.1", "js-cookie": "2.2.1", "jshint": "^2.13.4", "jspdf": "^2.5.1", "lodash": "^4.17.21", "luckysheet": "^2.1.13", "markdown-it": "^13.0.1", "markdown-it-link-attributes": "^4.0.1", "mathjs": "^11.6.0", "moment": "^2.29.1", "monaco-editor": "^0.29.1", "monaco-editor-webpack-plugin": "^5.0.0", "postcss": "^7.0.39", "reveal.js": "^4.4.0", "screenfull": "^4.2.1", "script-loader": "^0.7.2", "single-spa-vue": "1.8.2", "splitpanes": "^2.4.1", "systemjs-webpack-interop": "2.0.0", "tailwindcss": "npm:@tailwindcss/postcss7-compat", "vue": "2.6.11", "vue-color": "^2.8.1", "vue-count-to": "^1.0.13", "vue-grid-layout": "^2.3.12", "vue-i18n": "8.16.0", "vue-json-editor": "1.4.0", "vue-router": "3.1.5", "vue-seamless-scroll": "^1.1.23", "vue-sketch-ruler": "^1.0.3", "vue2-ace-editor": "^0.0.15", "vuedraggable": "^2.24.3", "vuex": "3.1.2", "vxe-table": "^3.4.14", "xe-utils": "^3.5.2", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/eslint-parser": "^7.19.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.19", "@vue/cli-plugin-router": "~4.2.0", "@vue/cli-plugin-vuex": "~4.2.0", "@vue/cli-service": "~4.2.0", "autoprefixer": "^9.8.8", "compression-webpack-plugin": "^5.0.1", "cross-env": "^7.0.3", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.14.1", "postcss": "^7.0.39", "sass-loader": "8.0.2", "script-loader": "^0.7.2", "svg-sprite-loader": "4.2.2", "tailwindcss": "npm:@tailwindcss/postcss7-compat", "vue-cli-plugin-single-spa": "1.1.0", "vue-template-compiler": "2.6.11", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^5.1.4"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}}